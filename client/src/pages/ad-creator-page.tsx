/**
 * Ad Creator Page - Redirección elegante a la nueva selección orbital
 */

import React, { useEffect } from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import { Megaphone, ArrowRight, Sparkles } from "lucide-react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

export default function AdCreatorPage() {
  const [, navigate] = useLocation();

  useEffect(() => {
    // Redirigir automáticamente después de una breve animación
    const timer = setTimeout(() => {
      navigate("/ad-creator/select");
    }, 1500);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <DashboardLayout>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="text-center"
        >
          <div className="relative bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-teal-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Megaphone className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Crear Anuncio
                </h1>
              </div>
              <p className="text-xl text-emerald-100 mb-6 max-w-3xl">
                Crea anuncios profesionales con calidad de agencia. Product placement perfecto, iluminación profesional y composición comercial.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Camera className="w-3 h-3 mr-1" />
                  Calidad de agencia
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Target className="w-3 h-3 mr-1" />
                  Product placement
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Edit3 className="w-3 h-3 mr-1" />
                  Edición profesional
                </Badge>
              </div>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg"
              >
                <Sparkles className="w-4 h-4 text-[#3018ef]" />
              </motion.div>
            </div>
          </div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className="text-3xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-4"
          >
            Creador de Anuncios
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="text-gray-600 mb-8 text-lg"
          >
            Preparando la selección de plataforma...
          </motion.p>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="flex items-center justify-center gap-2 text-[#3018ef]"
          >
            <span className="text-sm font-medium">Cargando</span>
            <motion.div
              animate={{ x: [0, 10, 0] }}
              transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }}
            >
              <ArrowRight className="w-4 h-4" />
            </motion.div>
          </motion.div>

          {/* Indicador de progreso */}
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: "100%" }}
            transition={{ delay: 1, duration: 1.5, ease: "easeInOut" }}
            className="mt-8 h-1 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full mx-auto max-w-xs"
          />
      </div>
    </DashboardLayout>
  );
}